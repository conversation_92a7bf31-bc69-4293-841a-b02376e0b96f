/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Ban Command Handler
 *
 * Handles user and server banning functionality for staff members.
 */

import { injectable, inject } from 'inversify';
import {
  SlashCommandBuilder,
  ApplicationCommandOptionType,
  ButtonBuilder,
  ButtonStyle,
  EmbedBuilder,
  type User,
  type ChatInputCommandInteraction
} from 'discord.js';
import { BaseCommandHandler, CommandCategory, type CommandResult } from '../BaseCommandHandler.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import type { UserDomainService } from '../../../domain/services/UserDomainService.js';
import type { Context } from '../../../shared/context/Context.js';

/**
 * Ban Command Handler
 *
 * Provides comprehensive banning functionality for staff members.
 */
@injectable()
export default class BanCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'ban',
    description: '🔨 Ban users or servers from InterChat with comprehensive options',
    category: CommandCategory.STAFF,
    staffOnly: true,
    ownerOnly: false,
    guildOnly: false,
    cooldown: 5000,
  };

  constructor(
    @inject(TYPES.UserDomainService)
    private readonly userDomainService: UserDomainService
  ) {
    super();
  }

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addStringOption(option =>
        option
          .setName('duration')
          .setDescription('Ban duration')
          .setRequired(true)
          .addChoices(
            { name: '⏰ 1 Hour', value: '1h' },
            { name: '📅 1 Day', value: '1d' },
            { name: '📆 1 Week', value: '1w' },
            { name: '🗓️ 30 Days', value: '30d' },
            { name: '♾️ Permanent', value: 'permanent' }
          )
      )
      .addStringOption(option =>
        option
          .setName('reason')
          .setDescription('Reason for the ban (required)')
          .setRequired(true)
          .setMaxLength(500)
      )
      .addUserOption(option =>
        option
          .setName('user')
          .setDescription('User to ban (required for user bans)')
          .setRequired(false)
      )
      .addStringOption(option =>
        option
          .setName('server_id')
          .setDescription('Server ID to ban (required for server bans)')
          .setRequired(false)
      );
  }

  async execute(context: Context, interaction: ChatInputCommandInteraction): Promise<CommandResult> {
    try {
      const user = interaction.options.getUser('user');
      const serverId = interaction.options.getString('server_id');
      const duration = interaction.options.getString('duration', true);
      const reason = interaction.options.getString('reason', true);

      // Validation
      if (user && serverId) {
        const embed = this.createErrorEmbed(
          'Invalid Input',
          'You cannot specify both a user and server ID. Please choose one.'
        );

        return {
          success: false,
          embed,
          ephemeral: true,
        };
      }

      if (!user && !serverId) {
        const embed = this.createErrorEmbed(
          'Invalid Input',
          'You must specify either a user or server ID to ban.'
        );

        return {
          success: false,
          embed,
          ephemeral: true,
        };
      }

      // Show confirmation dialog
      const confirmationEmbed = await this.createBanConfirmationEmbed(
        user ? 'user' : 'server',
        user,
        serverId,
        duration,
        reason,
        interaction.user.username
      );

      const confirmButton = new ButtonBuilder()
        .setCustomId(`ban_confirm_${user?.id || serverId}_${duration}_${Date.now()}`)
        .setLabel('Confirm Ban')
        .setStyle(ButtonStyle.Danger)
        .setEmoji('🔨');

      const cancelButton = new ButtonBuilder()
        .setCustomId(`ban_cancel_${Date.now()}`)
        .setLabel('Cancel')
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('❌');

      return {
        success: true,
        embed: confirmationEmbed,
        ephemeral: true,
        followUp: 'Ban confirmation displayed. Please review and confirm.',
      };

    } catch (error) {
      const embed = this.createErrorEmbed(
        'Ban Failed',
        'An error occurred while processing the ban command.'
      );

      return {
        success: false,
        embed,
        ephemeral: true,
      };
    }
  }

  private async createBanConfirmationEmbed(
    banType: 'user' | 'server',
    user: User | null,
    serverId: string | null,
    duration: string,
    reason: string,
    moderatorName: string
  ): Promise<EmbedBuilder> {
    let targetInfo = '';

    if (banType === 'user' && user) {
      targetInfo = `**User:** ${user.username} (${user.id})`;
    } else if (banType === 'server' && serverId) {
      // In a real implementation, you'd fetch server info
      targetInfo = `**Server:** ${serverId}`;
    }

    const durationText = duration === 'permanent' ? 'Permanent' : duration.toUpperCase();

    const embed = new EmbedBuilder()
      .setTitle('🔨 Confirm Ban')
      .setDescription('Review ban details before execution')
      .setColor(0xFF0000)
      .addFields(
        { name: 'Target', value: targetInfo, inline: false },
        { name: 'Type', value: banType === 'user' ? '👤 User Ban' : '🌐 Server Ban', inline: true },
        { name: 'Duration', value: durationText, inline: true },
        { name: 'Reason', value: reason, inline: false },
        { name: 'Moderator', value: moderatorName, inline: true }
      )
      .setTimestamp();

    return embed;
  }

  private parseDuration(duration: string): number | null {
    switch (duration) {
      case '1h':
        return 60 * 60 * 1000; // 1 hour in milliseconds
      case '1d':
        return 24 * 60 * 60 * 1000; // 1 day in milliseconds
      case '1w':
        return 7 * 24 * 60 * 60 * 1000; // 1 week in milliseconds
      case '30d':
        return 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds
      case 'permanent':
        return null; // Permanent ban
      default:
        return null;
    }
  }

  /**
   * Execute the actual ban (called by interaction handler)
   */
  async executeBan(
    userId: string | null,
    serverId: string | null,
    duration: string,
    reason: string,
    moderatorId: string
  ): Promise<boolean> {
    try {
      if (userId) {
        // Ban user using domain service
        const durationMs = this.parseDuration(duration);

        await this.userDomainService.banUser({
          userId,
          reason,
          moderatorId,
          duration: durationMs,
        });

        return true;
      } else if (serverId) {
        // Server banning would be implemented here
        // This would involve different logic and possibly different domain services
        console.log(`Server ban not yet implemented: ${serverId}`);
        return false;
      }

      return false;
    } catch (error) {
      console.error('Failed to execute ban:', error);
      return false;
    }
  }
}
