/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Badge Command Handler
 *
 * Handles user badge management functionality for staff members.
 */

import { injectable, inject } from 'inversify';
import {
  SlashCommandBuilder,
  type ChatInputCommandInteraction,
  type User
} from 'discord.js';
import { BaseCommandHandler, CommandCategory, type CommandResult } from '../BaseCommandHandler.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import type { IUserRepository } from '../../../domain/repositories/UserRepositories.js';
import type { Context } from '../../../shared/context/Context.js';

/**
 * Badge Command Handler
 *
 * Provides badge management functionality for staff members.
 */
@injectable()
export default class BadgeCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'badge',
    description: 'Manage user badges',
    category: CommandCategory.STAFF,
    staffOnly: true,
    ownerOnly: false,
    guildOnly: false,
    cooldown: 3000,
  };

  constructor(
    @inject(TYPES.UserRepository)
    private readonly userRepository: IUserRepository
  ) {
    super();
  }

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addSubcommand(subcommand =>
        subcommand
          .setName('add')
          .setDescription('Add a badge to a user')
          .addUserOption(option =>
            option
              .setName('user')
              .setDescription('The user to add the badge to')
              .setRequired(true)
          )
          .addStringOption(option =>
            option
              .setName('badge')
              .setDescription('The badge to add')
              .setRequired(true)
              .addChoices(
                { name: '🏆 Champion', value: 'CHAMPION' },
                { name: '🎨 Artist', value: 'ARTIST' },
                { name: '🛠️ Developer', value: 'DEVELOPER' },
                { name: '🎭 Supporter', value: 'SUPPORTER' },
                { name: '⭐ VIP', value: 'VIP' },
                { name: '🔧 Beta Tester', value: 'BETA_TESTER' }
              )
          )
      )
      .addSubcommand(subcommand =>
        subcommand
          .setName('remove')
          .setDescription('Remove a badge from a user')
          .addUserOption(option =>
            option
              .setName('user')
              .setDescription('The user to remove the badge from')
              .setRequired(true)
          )
          .addStringOption(option =>
            option
              .setName('badge')
              .setDescription('The badge to remove')
              .setRequired(true)
              .addChoices(
                { name: '🏆 Champion', value: 'CHAMPION' },
                { name: '🎨 Artist', value: 'ARTIST' },
                { name: '🛠️ Developer', value: 'DEVELOPER' },
                { name: '🎭 Supporter', value: 'SUPPORTER' },
                { name: '⭐ VIP', value: 'VIP' },
                { name: '🔧 Beta Tester', value: 'BETA_TESTER' }
              )
          )
      );
  }

  async execute(context: Context, interaction: ChatInputCommandInteraction): Promise<CommandResult> {
    try {
      const subcommand = interaction.options.getSubcommand();
      const targetUser = interaction.options.getUser('user', true);
      const badgeType = interaction.options.getString('badge', true);

      if (subcommand === 'add') {
        return await this.addBadge(targetUser, badgeType, interaction.user);
      } else if (subcommand === 'remove') {
        return await this.removeBadge(targetUser, badgeType, interaction.user);
      }

      const embed = this.createErrorEmbed(
        'Invalid Subcommand',
        'Please use either `add` or `remove` subcommand.'
      );

      return {
        success: false,
        embed,
        ephemeral: true,
      };

    } catch (error) {
      const embed = this.createErrorEmbed(
        'Badge Command Failed',
        'An error occurred while processing the badge command.'
      );

      return {
        success: false,
        embed,
        ephemeral: true,
      };
    }
  }

  private async addBadge(targetUser: User, badgeType: string, moderator: User): Promise<CommandResult> {
    try {
      // Check if user exists in our system
      const user = await this.userRepository.findById(targetUser.id);

      if (!user) {
        const embed = this.createErrorEmbed(
          'User Not Found',
          'This user is not registered in the InterChat system.'
        );

        return {
          success: false,
          embed,
          ephemeral: true,
        };
      }

      // In a real implementation, you would:
      // 1. Check if user already has this badge
      // 2. Add the badge to the user's badge collection
      // 3. Log the action for audit purposes

      // For now, we'll create a success message
      const embed = this.createSuccessEmbed(
        'Badge Added Successfully',
        `✅ Added **${this.getBadgeName(badgeType)}** badge to ${targetUser.username}\n\n` +
        `**Moderator:** ${moderator.username}\n` +
        `**Target:** ${targetUser.username} (${targetUser.id})`
      );

      return {
        success: true,
        embed,
        ephemeral: true,
      };

    } catch (error) {
      const embed = this.createErrorEmbed(
        'Failed to Add Badge',
        'An error occurred while adding the badge to the user.'
      );

      return {
        success: false,
        embed,
        ephemeral: true,
      };
    }
  }

  private async removeBadge(targetUser: User, badgeType: string, moderator: User): Promise<CommandResult> {
    try {
      // Check if user exists in our system
      const user = await this.userRepository.findById(targetUser.id);

      if (!user) {
        const embed = this.createErrorEmbed(
          'User Not Found',
          'This user is not registered in the InterChat system.'
        );

        return {
          success: false,
          embed,
          ephemeral: true,
        };
      }

      // In a real implementation, you would:
      // 1. Check if user has this badge
      // 2. Remove the badge from the user's badge collection
      // 3. Log the action for audit purposes

      // For now, we'll create a success message
      const embed = this.createSuccessEmbed(
        'Badge Removed Successfully',
        `✅ Removed **${this.getBadgeName(badgeType)}** badge from ${targetUser.username}\n\n` +
        `**Moderator:** ${moderator.username}\n` +
        `**Target:** ${targetUser.username} (${targetUser.id})`
      );

      return {
        success: true,
        embed,
        ephemeral: true,
      };

    } catch (error) {
      const embed = this.createErrorEmbed(
        'Failed to Remove Badge',
        'An error occurred while removing the badge from the user.'
      );

      return {
        success: false,
        embed,
        ephemeral: true,
      };
    }
  }

  private getBadgeName(badgeType: string): string {
    const badgeNames: Record<string, string> = {
      'CHAMPION': '🏆 Champion',
      'ARTIST': '🎨 Artist',
      'DEVELOPER': '🛠️ Developer',
      'SUPPORTER': '🎭 Supporter',
      'VIP': '⭐ VIP',
      'BETA_TESTER': '🔧 Beta Tester',
    };

    return badgeNames[badgeType] || badgeType;
  }
}
