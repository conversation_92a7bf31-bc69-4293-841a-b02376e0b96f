/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Recluster Command Handler
 *
 * Handles bot reclustering/restart functionality for developers.
 */

import { injectable } from 'inversify';
import { SlashCommandBuilder, type ChatInputCommandInteraction } from 'discord.js';
import { BaseCommandHandler, CommandCategory, type CommandResult } from '../BaseCommandHandler.js';
import type { Context } from '../../../shared/context/Context.js';

/**
 * Recluster Command Handler
 *
 * Provides bot restart functionality for developers.
 */
@injectable()
export default class Recluster<PERSON>ommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'recluster',
    description: 'Reboot the bot (Developer only)',
    category: CommandCategory.STAFF,
    staffOnly: true,
    ownerOnly: true, // Only bot owners can use this
    guildOnly: false,
    cooldown: 10000, // 10 second cooldown
  };

  buildCommand() {
    return new Slash<PERSON>ommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description);
  }

  async execute(context: Context, interaction: ChatInputCommandInteraction): Promise<CommandResult> {
    try {
      // Check if user is a developer (this would be configured in your system)
      const isDeveloper = this.isDeveloper(interaction.user.id);

      if (!isDeveloper) {
        const embed = this.createErrorEmbed(
          'Access Denied',
          'No u 😏'
        );

        return {
          success: false,
          embed,
          ephemeral: true,
        };
      }

      // Create success embed
      const embed = this.createSuccessEmbed(
        'Reclustering Bot',
        "I'll be back! 🤖"
      );

      // Send response first
      const result: CommandResult = {
        success: true,
        embed,
        ephemeral: true,
      };

      // Trigger recluster after a short delay to allow response to send
      setTimeout(() => {
        this.triggerRecluster(context);
      }, 1000);

      return result;

    } catch (error) {
      const embed = this.createErrorEmbed(
        'Recluster Failed',
        'An error occurred while attempting to recluster the bot.'
      );

      return {
        success: false,
        embed,
        ephemeral: true,
      };
    }
  }

  private isDeveloper(userId: string): boolean {
    // This should be configured through environment variables or configuration
    const developers = process.env.DEVELOPER_IDS?.split(',') || [];
    return developers.includes(userId);
  }

  private triggerRecluster(context: Context): void {
    try {
      // In the modern system, we'd use the cluster manager
      // For now, we'll use the legacy approach if available
      if (context.client && 'cluster' in context.client) {
        (context.client as any).cluster?.send('recluster');
      } else {
        // Alternative approach - exit process and let process manager restart
        console.log('Triggering bot restart...');
        process.exit(0);
      }
    } catch (error) {
      console.error('Failed to trigger recluster:', error);
    }
  }
}
