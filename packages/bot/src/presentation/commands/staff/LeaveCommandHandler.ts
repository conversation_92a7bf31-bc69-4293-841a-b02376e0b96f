/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Leave Command Handler
 *
 * Handles making the bot leave a specific server (developer only).
 */

import { injectable } from 'inversify';
import { SlashCommandBuilder, type ChatInputCommandInteraction } from 'discord.js';
import { BaseCommandHandler, CommandCategory, type CommandResult } from '../BaseCommandHandler.js';
import type { Context } from '../../../shared/context/Context.js';

/**
 * Leave Command Handler
 *
 * Provides functionality for developers to make the bot leave servers.
 */
@injectable()
export default class LeaveCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'leave',
    description: 'Make the bot leave a server (Developer only)',
    category: CommandCategory.STAFF,
    staffOnly: true,
    ownerOnly: true, // Only bot owners can use this
    guildOnly: false,
    cooldown: 5000,
  };

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addStringOption(option =>
        option
          .setName('server_id')
          .setDescription('The ID of the server to leave')
          .setRequired(true)
      );
  }

  async execute(context: Context, interaction: ChatInputCommandInteraction): Promise<CommandResult> {
    try {
      // Check if user is a developer
      const isDeveloper = this.isDeveloper(interaction.user.id);

      if (!isDeveloper) {
        const embed = this.createErrorEmbed(
          'Access Denied',
          '🚫 You are not authorized to use this command.'
        );

        return {
          success: false,
          embed,
          ephemeral: true,
        };
      }

      const serverId = interaction.options.getString('server_id', true);

      // Validate server ID format
      if (!/^\d{17,19}$/.test(serverId)) {
        const embed = this.createErrorEmbed(
          'Invalid Server ID',
          'Please provide a valid Discord server ID.'
        );

        return {
          success: false,
          embed,
          ephemeral: true,
        };
      }

      // Try to leave the server
      const result = await this.leaveServer(serverId, context);

      if (result.success) {
        const embed = this.createSuccessEmbed(
          'Server Left Successfully',
          `✅ Successfully left guild **${result.guildName}** (${serverId})`
        );

        return {
          success: true,
          embed,
          ephemeral: true,
        };
      } else {
        const embed = this.createErrorEmbed(
          'Failed to Leave Server',
          result.error || 'The bot is not in that server or an error occurred.'
        );

        return {
          success: false,
          embed,
          ephemeral: true,
        };
      }

    } catch (error) {
      const embed = this.createErrorEmbed(
        'Leave Command Failed',
        'An error occurred while attempting to leave the server.'
      );

      return {
        success: false,
        embed,
        ephemeral: true,
      };
    }
  }

  private isDeveloper(userId: string): boolean {
    // This should be configured through environment variables or configuration
    const developers = process.env.DEVELOPER_IDS?.split(',') || [];
    return developers.includes(userId);
  }

  private async leaveServer(serverId: string, context: Context): Promise<{
    success: boolean;
    guildName?: string;
    error?: string;
  }> {
    try {
      // Try to find the guild in the current client first
      const guild = context.client.guilds.cache.get(serverId);

      if (guild) {
        const guildName = guild.name;
        await guild.leave();
        return { success: true, guildName };
      }

      // If not found locally, try cluster broadcast (if available)
      if ('cluster' in context.client) {
        try {
          const results = await (context.client as any).cluster.broadcastEval(
            async (client: any, guildId: string) => {
              const guild = client.guilds.cache.get(guildId);
              if (guild) {
                const name = guild.name;
                await guild.leave();
                return { success: true, name };
              }
              return { success: false };
            },
            { context: serverId }
          );

          // Find the successful result
          const successResult = results.find((result: any) => result.success);
          if (successResult) {
            return { success: true, guildName: successResult.name };
          }
        } catch (clusterError) {
          console.error('Cluster broadcast failed:', clusterError);
        }
      }

      return { success: false, error: 'Bot is not in that server' };

    } catch (error) {
      console.error('Failed to leave server:', error);
      return { success: false, error: 'An error occurred while leaving the server' };
    }
  }
}
